@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom gear animation for auto parts theme */
@keyframes gear-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.gear-animation {
  animation: gear-spin 2s linear infinite;
}

/* Smooth gear animation with easing */
@keyframes gear-spin-smooth {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.gear-animation-smooth {
  animation: gear-spin-smooth 3s ease-in-out infinite;
}

/* Auto parts themed colors */
.auto-parts-primary {
  color: #1e40af; /* Blue-700 */
}

.auto-parts-secondary {
  color: #374151; /* Gray-700 */
}

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}
