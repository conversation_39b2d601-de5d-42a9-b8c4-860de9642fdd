{"name": "yamaha-rd-parts-shop", "version": "1.0.0", "description": "Yamaha RD Parts Shop - Full Stack Application", "main": "index.js", "scripts": {"dev": "node dev-server.js", "build": "cd frontend && npm run build", "vercel-build": "cd frontend && npm install && npm run build", "postinstall": "node install-bcryptjs.js || echo 'bcryptjs installation skipped'", "prestart": "node fix-bcrypt.js", "start": "node index.js"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "busboy": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "form-data": "^4.0.3", "jsonwebtoken": "^9.0.2", "nodemailer": "^7.0.5"}, "engines": {"node": ">=18.0.0"}, "devDependencies": {"vite": "^7.0.4"}}